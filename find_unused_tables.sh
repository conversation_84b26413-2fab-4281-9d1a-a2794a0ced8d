#!/bin/bash

echo "=== PHÂN TÍCH CÁC BẢNG KHÔNG SỬ DỤNG ==="
echo

# Danh sách các entities từ migration
entities=(
    "User" "UserProfile" "Category" "Product" "ProductCategory" "ProductImage" "ProductTag"
    "Brand" "ProductVariant" "ProductAttribute" "ProductAttributeTerm" "ProductAttributeValue" "ProductVariantAttribute"
    "Cart" "CartItem" "Order" "OrderItem" "OrderEvent" "Payment" "PaymentMethodEntity" "Refund"
    "FileUpload" "Address" "Wishlist" "UserPreference" "AccountVerification" "PasswordReset"
    "Review" "ReviewImage" "ReviewVote" "ProductRating"
    "Coupon" "CouponUsage" "Promotion" "LoyaltyProgram" "UserLoyaltyPoints"
    "Inventory" "InventoryMovement" "Warehouse" "StockAlert" "Supplier"
    "ShippingMethod" "ShippingZone" "ShippingRate" "Shipment" "ShipmentTracking" "Return" "ReturnItem"
    "Notification" "NotificationTemplate" "NotificationPreferences" "NotificationQueue"
    "Email" "EmailTemplate" "EmailSubscription"
    "AnalyticsEvent" "SalesReport" "ProductAnalytics" "UserAnalytics" "CategoryAnalytics" "SearchAnalytics"
    "SupportTicket" "TicketMessage" "TicketAttachment" "FAQ" "KnowledgeBase" "LiveChatSession" "ChatMessage"
    "UserProductInteraction" "ProductRecommendation" "ProductSimilarity" "FrequentlyBoughtTogether" "TrendingProduct"
    "ProductComparison" "ProductComparisonItem" "FilterSet" "FilterUsage" "ProductFilterOption"
)

echo "Kiểm tra ${#entities[@]} entities..."
echo

unused_count=0
low_usage_count=0
unused_entities=()
low_usage_entities=()

for entity in "${entities[@]}"; do
    # Đếm số lần xuất hiện trong code (loại trừ định nghĩa struct)
    count=$(grep -r --include="*.go" "$entity" . | grep -v "type $entity struct" | grep -v "func.*$entity.*struct" | wc -l)
    
    if [ "$count" -eq 0 ]; then
        unused_entities+=("$entity")
        unused_count=$((unused_count + 1))
    elif [ "$count" -lt 5 ]; then
        low_usage_entities+=("$entity")
        low_usage_count=$((low_usage_count + 1))
    fi
done

echo "=== KẾT QUẢ ==="
echo
echo "📊 THỐNG KÊ:"
echo "  - Tổng entities kiểm tra: ${#entities[@]}"
echo "  - Entities không sử dụng: $unused_count"
echo "  - Entities ít sử dụng (<5 refs): $low_usage_count"
echo "  - Entities đang sử dụng: $((${#entities[@]} - unused_count - low_usage_count))"
echo

if [ ${#unused_entities[@]} -gt 0 ]; then
    echo "🚨 ENTITIES KHÔNG SỬ DỤNG ($unused_count):"
    for entity in "${unused_entities[@]}"; do
        echo "  - $entity"
    done
    echo
fi

if [ ${#low_usage_entities[@]} -gt 0 ]; then
    echo "⚠️  ENTITIES ÍT SỬ DỤNG ($low_usage_count):"
    for entity in "${low_usage_entities[@]}"; do
        count=$(grep -r --include="*.go" "$entity" . | grep -v "type $entity struct" | wc -l)
        echo "  - $entity ($count references)"
    done
    echo
fi

echo "=== KIỂM TRA REPOSITORY ==="
echo "Repositories được implement:"
find internal/infrastructure/database -name "*_repository.go" | sed 's/.*\//  - /' | sed 's/_repository.go//'
echo

echo "=== KIỂM TRA USECASE ==="
echo "Usecases được implement:"
find internal/usecases -name "*.go" | grep -v ".bak\|.tmp" | sed 's/.*\//  - /' | sed 's/_usecase.go\|.go//'
echo

echo "=== KHUYẾN NGHỊ ==="
if [ $unused_count -gt 0 ]; then
    echo "🔧 Có thể loại bỏ $unused_count entities không sử dụng"
fi
if [ $low_usage_count -gt 5 ]; then
    echo "🔧 Xem xét gộp hoặc đơn giản hóa $low_usage_count entities ít sử dụng"
fi
if [ ${#entities[@]} -gt 50 ]; then
    echo "🔧 Tổng số entities (${#entities[@]}) vượt quá khuyến nghị (50)"
fi
