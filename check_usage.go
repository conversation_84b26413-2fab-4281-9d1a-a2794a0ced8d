package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

func main() {
	// List of entities from migration
	migrationEntities := []string{
		"User", "UserProfile", "Category", "Product", "ProductCategory", "ProductImage", "ProductTag",
		"Brand", "ProductVariant", "ProductAttribute", "ProductAttributeTerm", "ProductAttributeValue", "ProductVariantAttribute",
		"Cart", "CartItem", "Order", "OrderItem", "OrderEvent", "Payment", "PaymentMethodEntity", "Refund",
		"FileUpload", "Address", "Wishlist", "UserPreference", "AccountVerification", "PasswordReset",
		"Review", "ReviewImage", "ReviewVote", "ProductRating",
		"Coupon", "CouponUsage", "Promotion", "LoyaltyProgram", "UserLoyaltyPoints",
		"Inventory", "InventoryMovement", "Warehouse", "StockAlert", "Supplier",
		"ShippingMethod", "ShippingZone", "ShippingRate", "Shipment", "ShipmentTracking", "Return", "ReturnItem",
		"Notification", "NotificationTemplate", "NotificationPreferences", "NotificationQueue",
		"Email", "EmailTemplate", "EmailSubscription",
		"AnalyticsEvent", "SalesReport", "ProductAnalytics", "UserAnalytics", "CategoryAnalytics", "SearchAnalytics",
		"SupportTicket", "TicketMessage", "TicketAttachment", "FAQ", "KnowledgeBase", "LiveChatSession", "ChatMessage",
		"UserProductInteraction", "ProductRecommendation", "ProductSimilarity", "FrequentlyBoughtTogether", "TrendingProduct",
		"ProductComparison", "ProductComparisonItem", "FilterSet", "FilterUsage", "ProductFilterOption",
	}

	fmt.Printf("=== USAGE ANALYSIS ===\n\n")
	
	usedEntities := make(map[string]int)
	unusedEntities := []string{}
	
	for _, entity := range migrationEntities {
		count := checkEntityUsage(entity)
		usedEntities[entity] = count
		if count == 0 {
			unusedEntities = append(unusedEntities, entity)
		}
	}
	
	// Sort by usage
	fmt.Printf("ENTITIES BY USAGE:\n")
	for entity, count := range usedEntities {
		if count > 0 {
			fmt.Printf("  %s: %d references\n", entity, count)
		}
	}
	
	fmt.Printf("\nUNUSED ENTITIES (%d):\n", len(unusedEntities))
	for _, entity := range unusedEntities {
		fmt.Printf("  - %s\n", entity)
	}
	
	// Check repository usage
	fmt.Printf("\n=== REPOSITORY USAGE ===\n")
	checkRepositoryUsage()
	
	// Check service usage  
	fmt.Printf("\n=== SERVICE USAGE ===\n")
	checkServiceUsage()
}

func checkEntityUsage(entity string) int {
	// Search for entity usage in Go files
	cmd := exec.Command("grep", "-r", "--include=*.go", entity, ".")
	output, err := cmd.Output()
	if err != nil {
		return 0
	}
	
	lines := strings.Split(string(output), "\n")
	count := 0
	for _, line := range lines {
		if strings.TrimSpace(line) != "" && !strings.Contains(line, "type "+entity+" struct") {
			count++
		}
	}
	return count
}

func checkRepositoryUsage() {
	repoDir := "internal/infrastructure/repository"
	
	err := filepath.Walk(repoDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if strings.HasSuffix(path, "_repository.go") {
			fmt.Printf("  - %s\n", filepath.Base(path))
		}
		return nil
	})
	
	if err != nil {
		fmt.Printf("Error walking repository directory: %v\n", err)
	}
}

func checkServiceUsage() {
	serviceDir := "internal/application/services"
	
	err := filepath.Walk(serviceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if strings.HasSuffix(path, "_service.go") {
			fmt.Printf("  - %s\n", filepath.Base(path))
		}
		return nil
	})
	
	if err != nil {
		fmt.Printf("Error walking service directory: %v\n", err)
	}
}
