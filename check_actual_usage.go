package main

import (
	"fmt"
	"os/exec"
	"strings"

	"ecom-golang-clean-architecture/internal/infrastructure/config"
	"ecom-golang-clean-architecture/internal/infrastructure/database"

	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// Connect to database
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		fmt.Printf("Failed to connect to database: %v\n", err)
		return
	}

	fmt.Printf("=== KIỂM TRA BẢNG THỰC SỰ ĐƯỢC SỬ DỤNG TRONG CODE ===\n\n")
	fmt.Printf("Database: %s\n", cfg.Database.Name)
	fmt.Printf("Host: %s:%s\n\n", cfg.Database.Host, cfg.Database.Port)

	// Get all tables from database
	allTables, err := getAllTablesFromDB(db)
	if err != nil {
		fmt.Printf("Error getting tables: %v\n", err)
		return
	}

	fmt.Printf("📊 TỔNG SỐ BẢNG TRONG DATABASE: %d\n\n", len(allTables))

	// Check each table usage in code
	usedTables := []TableUsage{}
	unusedTables := []string{}

	fmt.Printf("🔍 Đang kiểm tra từng bảng...\n\n")

	for i, tableName := range allTables {
		fmt.Printf("Checking %d/%d: %s", i+1, len(allTables), tableName)
		
		usage := checkTableUsageInCode(tableName)
		if usage.IsUsed {
			usedTables = append(usedTables, usage)
			fmt.Printf(" ✅ USED (%d refs)\n", usage.References)
		} else {
			unusedTables = append(unusedTables, tableName)
			fmt.Printf(" ❌ NOT USED\n")
		}
	}

	// Print results
	fmt.Printf("\n=== KẾT QUẢ ===\n\n")
	
	fmt.Printf("✅ BẢNG ĐƯỢC SỬ DỤNG TRONG CODE (%d/%d):\n", len(usedTables), len(allTables))
	for _, usage := range usedTables {
		fmt.Printf("   🔹 %-30s (%d references)\n", usage.TableName, usage.References)
		if len(usage.FoundIn) > 0 {
			fmt.Printf("      Found in: %s\n", strings.Join(usage.FoundIn, ", "))
		}
	}

	fmt.Printf("\n❌ BẢNG KHÔNG ĐƯỢC SỬ DỤNG TRONG CODE (%d/%d):\n", len(unusedTables), len(allTables))
	for _, tableName := range unusedTables {
		fmt.Printf("   - %s\n", tableName)
	}

	// Summary
	fmt.Printf("\n📊 THỐNG KÊ:\n")
	fmt.Printf("   - Tổng bảng: %d\n", len(allTables))
	fmt.Printf("   - Được sử dụng: %d (%.1f%%)\n", len(usedTables), float64(len(usedTables))*100/float64(len(allTables)))
	fmt.Printf("   - Không sử dụng: %d (%.1f%%)\n", len(unusedTables), float64(len(unusedTables))*100/float64(len(allTables)))

	if len(unusedTables) > 0 {
		fmt.Printf("\n⚠️  CÓ %d BẢNG KHÔNG ĐƯỢC SỬ DỤNG TRONG CODE!\n", len(unusedTables))
		fmt.Printf("   Những bảng này có thể được tạo nhầm hoặc từ migration cũ.\n")
	}
}

type TableUsage struct {
	TableName  string
	IsUsed     bool
	References int
	FoundIn    []string
}

func getAllTablesFromDB(db *gorm.DB) ([]string, error) {
	var tables []string
	
	query := `
		SELECT tablename 
		FROM pg_tables 
		WHERE schemaname = 'public' 
		ORDER BY tablename
	`
	
	rows, err := db.Raw(query).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return nil, err
		}
		tables = append(tables, tableName)
	}

	return tables, nil
}

func checkTableUsageInCode(tableName string) TableUsage {
	usage := TableUsage{
		TableName: tableName,
		FoundIn:   []string{},
	}

	// Convert table name to possible entity names
	possibleNames := generatePossibleNames(tableName)
	
	totalRefs := 0
	foundIn := make(map[string]bool)

	for _, name := range possibleNames {
		// Search in Go files
		refs, files := searchInGoFiles(name)
		totalRefs += refs
		for _, file := range files {
			foundIn[file] = true
		}

		// Search for table name directly (in migrations, raw queries)
		refs2, files2 := searchTableNameDirectly(tableName)
		totalRefs += refs2
		for _, file := range files2 {
			foundIn[file] = true
		}
	}

	// Convert map to slice
	for file := range foundIn {
		usage.FoundIn = append(usage.FoundIn, file)
	}

	usage.References = totalRefs
	usage.IsUsed = totalRefs > 0

	return usage
}

func generatePossibleNames(tableName string) []string {
	names := []string{}
	
	// Original table name
	names = append(names, tableName)
	
	// Convert to singular entity name (users -> User)
	singular := strings.TrimSuffix(tableName, "s")
	if singular != tableName {
		names = append(names, strings.Title(singular))
	}
	
	// Convert snake_case to PascalCase (user_profiles -> UserProfile)
	parts := strings.Split(tableName, "_")
	if len(parts) > 1 {
		pascalCase := ""
		for _, part := range parts {
			// Remove 's' from last part if it's plural
			if part == parts[len(parts)-1] && strings.HasSuffix(part, "s") && len(part) > 1 {
				part = strings.TrimSuffix(part, "s")
			}
			pascalCase += strings.Title(part)
		}
		names = append(names, pascalCase)
	}
	
	return names
}

func searchInGoFiles(searchTerm string) (int, []string) {
	cmd := exec.Command("grep", "-r", "--include=*.go", "-l", searchTerm, ".")
	output, err := cmd.Output()
	if err != nil {
		return 0, []string{}
	}

	files := []string{}
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	for _, line := range lines {
		if line != "" {
			files = append(files, line)
		}
	}

	// Count total references
	cmd2 := exec.Command("grep", "-r", "--include=*.go", searchTerm, ".")
	output2, err2 := cmd2.Output()
	if err2 != nil {
		return 0, files
	}

	refCount := len(strings.Split(strings.TrimSpace(string(output2)), "\n"))
	if string(output2) == "" {
		refCount = 0
	}

	return refCount, files
}

func searchTableNameDirectly(tableName string) (int, []string) {
	// Search for table name in quotes (for raw SQL)
	patterns := []string{
		fmt.Sprintf(`"%s"`, tableName),
		fmt.Sprintf("'%s'", tableName),
		fmt.Sprintf("`%s`", tableName),
		tableName,
	}

	totalRefs := 0
	allFiles := make(map[string]bool)

	for _, pattern := range patterns {
		cmd := exec.Command("grep", "-r", "--include=*.go", "-l", pattern, ".")
		output, err := cmd.Output()
		if err != nil {
			continue
		}

		lines := strings.Split(strings.TrimSpace(string(output)), "\n")
		for _, line := range lines {
			if line != "" {
				allFiles[line] = true
			}
		}

		// Count references
		cmd2 := exec.Command("grep", "-r", "--include=*.go", pattern, ".")
		output2, err2 := cmd2.Output()
		if err2 == nil && string(output2) != "" {
			totalRefs += len(strings.Split(strings.TrimSpace(string(output2)), "\n"))
		}
	}

	files := []string{}
	for file := range allFiles {
		files = append(files, file)
	}

	return totalRefs, files
}
