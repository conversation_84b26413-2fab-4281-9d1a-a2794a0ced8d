package main

import (
	"fmt"
	"os/exec"
	"strings"

	"ecom-golang-clean-architecture/internal/infrastructure/config"
	"ecom-golang-clean-architecture/internal/infrastructure/database"

	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// Connect to database
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		fmt.Printf("Failed to connect to database: %v\n", err)
		return
	}

	fmt.Printf("=== LẤY TẤT CẢ BẢNG TRONG DATABASE ===\n\n")
	fmt.Printf("Database: %s\n", cfg.Database.Name)
	fmt.Printf("Host: %s:%s\n\n", cfg.Database.Host, cfg.Database.Port)

	// Get all tables from all schemas
	allTables, err := getAllTablesAllSchemas(db)
	if err != nil {
		fmt.Printf("Error getting tables: %v\n", err)
		return
	}

	fmt.Printf("📊 TỔNG SỐ BẢNG TRONG DATABASE: %d\n\n", len(allTables))

	// Group by schema
	schemaGroups := make(map[string][]string)
	for _, table := range allTables {
		parts := strings.Split(table, ".")
		if len(parts) == 2 {
			schema := parts[0]
			tableName := parts[1]
			schemaGroups[schema] = append(schemaGroups[schema], tableName)
		}
	}

	// Print by schema
	for schema, tables := range schemaGroups {
		fmt.Printf("📁 Schema: %s (%d bảng)\n", schema, len(tables))
		for i, table := range tables {
			fmt.Printf("   %3d. %s\n", i+1, table)
		}
		fmt.Printf("\n")
	}

	// Check usage for public schema tables only
	fmt.Printf("🔍 KIỂM TRA SỬ DỤNG CHỈ CHO SCHEMA PUBLIC:\n\n")
	
	publicTables := schemaGroups["public"]
	if len(publicTables) == 0 {
		fmt.Printf("Không tìm thấy bảng nào trong schema public\n")
		return
	}

	usedTables := []string{}
	unusedTables := []string{}

	for i, tableName := range publicTables {
		fmt.Printf("Checking %d/%d: %s", i+1, len(publicTables), tableName)
		
		if isTableUsedInCode(tableName) {
			usedTables = append(usedTables, tableName)
			fmt.Printf(" ✅ USED\n")
		} else {
			unusedTables = append(unusedTables, tableName)
			fmt.Printf(" ❌ NOT USED\n")
		}
	}

	// Print results
	fmt.Printf("\n=== KẾT QUẢ CHO SCHEMA PUBLIC ===\n\n")
	
	fmt.Printf("✅ BẢNG ĐƯỢC SỬ DỤNG (%d/%d):\n", len(usedTables), len(publicTables))
	for _, table := range usedTables {
		fmt.Printf("   🔹 %s\n", table)
	}

	fmt.Printf("\n❌ BẢNG KHÔNG ĐƯỢC SỬ DỤNG (%d/%d):\n", len(unusedTables), len(publicTables))
	for _, table := range unusedTables {
		fmt.Printf("   - %s\n", table)
	}

	// Summary
	fmt.Printf("\n📊 THỐNG KÊ TỔNG HỢP:\n")
	fmt.Printf("   - Tổng bảng trong database: %d\n", len(allTables))
	fmt.Printf("   - Bảng trong schema public: %d\n", len(publicTables))
	fmt.Printf("   - Bảng public được sử dụng: %d (%.1f%%)\n", len(usedTables), float64(len(usedTables))*100/float64(len(publicTables)))
	fmt.Printf("   - Bảng public không sử dụng: %d (%.1f%%)\n", len(unusedTables), float64(len(unusedTables))*100/float64(len(publicTables)))

	// Save results
	saveResults(allTables, usedTables, unusedTables)
}

func getAllTablesAllSchemas(db *gorm.DB) ([]string, error) {
	var tables []string
	
	// Query to get all tables from all schemas (including system schemas)
	query := `
		SELECT schemaname || '.' || tablename as full_table_name
		FROM pg_tables 
		WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
		ORDER BY schemaname, tablename
	`
	
	rows, err := db.Raw(query).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var fullTableName string
		if err := rows.Scan(&fullTableName); err != nil {
			return nil, err
		}
		tables = append(tables, fullTableName)
	}

	// Also try to get all tables including system ones
	query2 := `
		SELECT schemaname || '.' || tablename as full_table_name
		FROM pg_tables 
		ORDER BY schemaname, tablename
	`
	
	rows2, err := db.Raw(query2).Rows()
	if err == nil {
		defer rows2.Close()
		
		allSystemTables := []string{}
		for rows2.Next() {
			var fullTableName string
			if err := rows2.Scan(&fullTableName); err == nil {
				allSystemTables = append(allSystemTables, fullTableName)
			}
		}
		
		fmt.Printf("📋 Tổng số bảng (bao gồm system): %d\n", len(allSystemTables))
		fmt.Printf("📋 Tổng số bảng (loại trừ system): %d\n", len(tables))
	}

	return tables, nil
}

func isTableUsedInCode(tableName string) bool {
	// Convert table name to possible entity names
	possibleNames := generatePossibleNames(tableName)
	
	for _, name := range possibleNames {
		// Search in Go files
		if searchInGoFiles(name) {
			return true
		}
	}
	
	// Search for table name directly
	return searchTableNameDirectly(tableName)
}

func generatePossibleNames(tableName string) []string {
	names := []string{tableName}
	
	// Convert to singular entity name (users -> User)
	singular := strings.TrimSuffix(tableName, "s")
	if singular != tableName {
		names = append(names, strings.Title(singular))
	}
	
	// Convert snake_case to PascalCase (user_profiles -> UserProfile)
	parts := strings.Split(tableName, "_")
	if len(parts) > 1 {
		pascalCase := ""
		for _, part := range parts {
			if part == parts[len(parts)-1] && strings.HasSuffix(part, "s") && len(part) > 1 {
				part = strings.TrimSuffix(part, "s")
			}
			pascalCase += strings.Title(part)
		}
		names = append(names, pascalCase)
	}
	
	return names
}

func searchInGoFiles(searchTerm string) bool {
	cmd := exec.Command("grep", "-r", "--include=*.go", "-q", searchTerm, ".")
	err := cmd.Run()
	return err == nil
}

func searchTableNameDirectly(tableName string) bool {
	patterns := []string{
		fmt.Sprintf(`"%s"`, tableName),
		fmt.Sprintf("'%s'", tableName),
		tableName,
	}

	for _, pattern := range patterns {
		cmd := exec.Command("grep", "-r", "--include=*.go", "-q", pattern, ".")
		if cmd.Run() == nil {
			return true
		}
	}
	return false
}

func saveResults(allTables, usedTables, unusedTables []string) {
	// Save all tables
	fmt.Printf("\n📁 Lưu kết quả vào files:\n")
	
	// Write all tables
	allTablesContent := strings.Join(allTables, "\n")
	if err := writeFile("all_tables_117.txt", allTablesContent); err == nil {
		fmt.Printf("   - all_tables_117.txt (%d bảng)\n", len(allTables))
	}
	
	// Write used tables
	usedTablesContent := strings.Join(usedTables, "\n")
	if err := writeFile("used_tables_public.txt", usedTablesContent); err == nil {
		fmt.Printf("   - used_tables_public.txt (%d bảng)\n", len(usedTables))
	}
	
	// Write unused tables
	unusedTablesContent := strings.Join(unusedTables, "\n")
	if err := writeFile("unused_tables_public.txt", unusedTablesContent); err == nil {
		fmt.Printf("   - unused_tables_public.txt (%d bảng)\n", len(unusedTables))
	}
}

func writeFile(filename, content string) error {
	cmd := exec.Command("sh", "-c", fmt.Sprintf("echo '%s' > %s", content, filename))
	return cmd.Run()
}
