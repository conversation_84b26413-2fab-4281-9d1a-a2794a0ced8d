#!/bin/bash

echo "=== KIỂM TRA NHANH BẢNG ĐƯỢC SỬ DỤNG TRONG CODE ==="
echo

# L<PERSON>y danh sách tất cả bảng từ database
echo "🔍 L<PERSON>y danh sách bảng từ database..."

# Kết nối database và lấy danh sách bảng
PGPASSWORD="password" psql -h localhost -U postgres -d ecommerce_db -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;" > /tmp/all_tables.txt 2>/dev/null

if [ ! -f /tmp/all_tables.txt ]; then
    echo "❌ Không thể kết nối database. Sử dụng danh sách bảng từ kết quả trước:"
    # Danh sách bảng từ kết quả check trước đó
    cat > /tmp/all_tables.txt << 'EOF'
 account_verifications
 addresses
 analytics_events
 brands
 cart_items
 carts
 categories
 category_analytics
 chat_messages
 coupon_categories
 coupon_products
 coupon_usage
 coupon_users
 coupons
 email_subscriptions
 email_templates
 emails
 faqs
 file_uploads
 filter_sets
 filter_usage
 frequently_bought_together
 inventories
 inventory_movements
 knowledge_bases
 live_chat_sessions
 loyalty_programs
 migration_records
 notification_preferences
 notification_queues
 notification_templates
 notifications
 order_events
 order_items
 orders
 password_resets
 payment_method_entities
 payments
 popular_searches
 product_analytics
 product_attribute_terms
 product_attribute_values
 product_attributes
 product_categories
 product_comparisons
 product_filter_options
 product_images
 product_ratings
 product_recommendations
 product_similarities
 product_tags
 product_variant_attributes
 product_variants
 products
 promotion_categories
 promotion_products
 promotions
 refunds
 return_items
 returns
 review_images
 review_votes
 reviews
 sales_reports
 search_analytics
 search_events
 search_filters
 search_histories
 search_suggestions
 shipment_tracking
 shipments
 shipping_methods
 shipping_rates
 shipping_zones
 stock_alerts
 supplier_products
 suppliers
 support_tickets
 ticket_attachments
 ticket_messages
 trending_products
 user_activity_logs
 user_analytics
 user_loyalty_points
 user_preferences
 user_product_interactions
 user_profiles
 user_sessions
 user_wishlists
 warehouses
EOF
fi

# Đếm tổng số bảng
total_tables=$(cat /tmp/all_tables.txt | wc -l)
echo "📊 Tổng số bảng: $total_tables"
echo

# Tạo file kết quả
used_tables=()
unused_tables=()

echo "🔍 Kiểm tra từng bảng trong code..."

while IFS= read -r table_name; do
    # Loại bỏ khoảng trắng
    table_name=$(echo "$table_name" | xargs)
    
    if [ -z "$table_name" ]; then
        continue
    fi
    
    # Chuyển đổi table name thành entity name có thể có
    # users -> User, user_profiles -> UserProfile
    entity_name=$(echo "$table_name" | sed 's/_/ /g' | sed 's/s$//' | awk '{for(i=1;i<=NF;i++) $i=toupper(substr($i,1,1)) tolower(substr($i,2))}1' | sed 's/ //g')
    
    # Kiểm tra trong code
    found=false
    
    # Tìm entity name trong code
    if grep -r --include="*.go" -q "$entity_name" . 2>/dev/null; then
        found=true
    fi
    
    # Tìm table name trực tiếp (trong migration, raw SQL)
    if grep -r --include="*.go" -q "\"$table_name\"" . 2>/dev/null; then
        found=true
    fi
    
    if grep -r --include="*.go" -q "'$table_name'" . 2>/dev/null; then
        found=true
    fi
    
    # Tìm trong TableName() method
    if grep -r --include="*.go" -q "\"$table_name\"" . 2>/dev/null; then
        found=true
    fi
    
    if [ "$found" = true ]; then
        used_tables+=("$table_name")
        echo "   ✅ $table_name"
    else
        unused_tables+=("$table_name")
        echo "   ❌ $table_name"
    fi
    
done < /tmp/all_tables.txt

echo
echo "=== KẾT QUẢ ==="
echo

echo "✅ BẢNG ĐƯỢC SỬ DỤNG TRONG CODE (${#used_tables[@]}/$total_tables):"
for table in "${used_tables[@]}"; do
    echo "   🔹 $table"
done

echo
echo "❌ BẢNG KHÔNG ĐƯỢC SỬ DỤNG TRONG CODE (${#unused_tables[@]}/$total_tables):"
for table in "${unused_tables[@]}"; do
    echo "   - $table"
done

echo
echo "📊 THỐNG KÊ:"
used_count=${#used_tables[@]}
unused_count=${#unused_tables[@]}
used_percent=$((used_count * 100 / total_tables))
unused_percent=$((unused_count * 100 / total_tables))

echo "   - Tổng bảng: $total_tables"
echo "   - Được sử dụng: $used_count ($used_percent%)"
echo "   - Không sử dụng: $unused_count ($unused_percent%)"

if [ $unused_count -gt 0 ]; then
    echo
    echo "⚠️  CÓ $unused_count BẢNG KHÔNG ĐƯỢC SỬ DỤNG TRONG CODE!"
    echo "   Những bảng này có thể:"
    echo "   - Được tạo từ migration cũ"
    echo "   - Là bảng test hoặc thử nghiệm"
    echo "   - Được tạo nhầm"
    echo "   - Từ các feature đã bị xóa"
fi

# Lưu kết quả vào file
echo "📁 Lưu kết quả vào files:"
printf '%s\n' "${used_tables[@]}" > used_tables.txt
printf '%s\n' "${unused_tables[@]}" > unused_tables.txt
echo "   - used_tables.txt (${#used_tables[@]} bảng)"
echo "   - unused_tables.txt (${#unused_tables[@]} bảng)"

# Cleanup
rm -f /tmp/all_tables.txt
