package main

import (
	"fmt"
	"log"
	"strings"

	"ecom-golang-clean-architecture/internal/infrastructure/config"
	"ecom-golang-clean-architecture/internal/infrastructure/database"

	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Connect to database
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	fmt.Printf("=== KIỂM TRA BẢNG THỰC TẾ TRONG DATABASE ===\n\n")
	fmt.Printf("Database: %s\n", cfg.Database.Name)
	fmt.Printf("Host: %s:%s\n\n", cfg.Database.Host, cfg.Database.Port)

	// Get all tables in the database
	tables, err := getAllTables(db)
	if err != nil {
		log.Fatal("Failed to get tables:", err)
	}

	fmt.Printf("📊 TỔNG SỐ BẢNG TRONG DATABASE: %d\n\n", len(tables))

	// Categorize tables
	coreTables := []string{}
	supportTables := []string{}
	analyticsTables := []string{}
	systemTables := []string{}
	joinTables := []string{}

	for _, table := range tables {
		if isSystemTable(table) {
			systemTables = append(systemTables, table)
		} else if isJoinTableName(table) {
			joinTables = append(joinTables, table)
		} else if isCoreTableName(table) {
			coreTables = append(coreTables, table)
		} else if isAnalyticsTableName(table) {
			analyticsTables = append(analyticsTables, table)
		} else {
			supportTables = append(supportTables, table)
		}
	}

	fmt.Printf("🔧 CORE BUSINESS TABLES (%d):\n", len(coreTables))
	for _, table := range coreTables {
		count, _ := getTableRowCount(db, table)
		fmt.Printf("  - %s (%d rows)\n", table, count)
	}

	fmt.Printf("\n📋 SUPPORT/FEATURE TABLES (%d):\n", len(supportTables))
	for _, table := range supportTables {
		count, _ := getTableRowCount(db, table)
		fmt.Printf("  - %s (%d rows)\n", table, count)
	}

	fmt.Printf("\n📈 ANALYTICS TABLES (%d):\n", len(analyticsTables))
	for _, table := range analyticsTables {
		count, _ := getTableRowCount(db, table)
		fmt.Printf("  - %s (%d rows)\n", table, count)
	}

	fmt.Printf("\n🔗 JOIN TABLES (%d):\n", len(joinTables))
	for _, table := range joinTables {
		count, _ := getTableRowCount(db, table)
		fmt.Printf("  - %s (%d rows)\n", table, count)
	}

	fmt.Printf("\n⚙️  SYSTEM TABLES (%d):\n", len(systemTables))
	for _, table := range systemTables {
		count, _ := getTableRowCount(db, table)
		fmt.Printf("  - %s (%d rows)\n", table, count)
	}

	// Find empty tables
	emptyTables := []string{}
	for _, table := range tables {
		if !isSystemTable(table) {
			count, err := getTableRowCount(db, table)
			if err == nil && count == 0 {
				emptyTables = append(emptyTables, table)
			}
		}
	}

	fmt.Printf("\n🗑️  EMPTY TABLES (%d):\n", len(emptyTables))
	for _, table := range emptyTables {
		fmt.Printf("  - %s\n", table)
	}

	fmt.Printf("\n=== KHUYẾN NGHỊ ===\n")
	if len(tables) > 80 {
		fmt.Printf("⚠️  Quá nhiều bảng (%d). Khuyến nghị giảm xuống dưới 80.\n", len(tables))
	}
	if len(emptyTables) > 10 {
		fmt.Printf("⚠️  Có %d bảng trống. Xem xét loại bỏ nếu không cần thiết.\n", len(emptyTables))
	}
	if len(analyticsTables) > 10 {
		fmt.Printf("⚠️  Quá nhiều bảng analytics (%d). Xem xét gộp lại.\n", len(analyticsTables))
	}

	fmt.Printf("\n✅ Phân tích hoàn tất!\n")
}

func getAllTables(db *gorm.DB) ([]string, error) {
	var tables []string
	
	query := `
		SELECT tablename 
		FROM pg_tables 
		WHERE schemaname = 'public' 
		ORDER BY tablename
	`
	
	rows, err := db.Raw(query).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return nil, err
		}
		tables = append(tables, tableName)
	}

	return tables, nil
}

func getTableRowCount(db *gorm.DB, tableName string) (int64, error) {
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM " + tableName).Scan(&count).Error
	return count, err
}

func isSystemTable(tableName string) bool {
	systemTables := []string{
		"migration_records", "schema_migrations", "ar_internal_metadata",
	}
	for _, sys := range systemTables {
		if tableName == sys {
			return true
		}
	}
	return false
}

func isCoreTableName(tableName string) bool {
	coreTables := []string{
		"users", "user_profiles", "products", "categories", "orders", "order_items",
		"carts", "cart_items", "payments", "inventories", "brands",
	}
	for _, core := range coreTables {
		if tableName == core {
			return true
		}
	}
	return false
}

func isAnalyticsTableName(tableName string) bool {
	patterns := []string{
		"analytics", "reports", "events", "tracking", "stats", "metrics",
	}
	for _, pattern := range patterns {
		if strings.Contains(tableName, pattern) {
			return true
		}
	}
	return false
}

func isJoinTableName(tableName string) bool {
	patterns := []string{
		"user_", "product_", "category_",
	}
	count := 0
	for _, pattern := range patterns {
		if strings.Contains(tableName, pattern) {
			count++
		}
	}
	return count >= 2 || strings.Contains(tableName, "_tags") || strings.Contains(tableName, "_categories")
}
