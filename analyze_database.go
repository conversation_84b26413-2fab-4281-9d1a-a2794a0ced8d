package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"strings"
)

type TableInfo struct {
	Name         string
	FileName     string
	Fields       []string
	Relationships []string
	IsJoinTable  bool
}

func main() {
	entitiesDir := "internal/domain/entities"
	tables := make(map[string]*TableInfo)
	
	err := filepath.Walk(entitiesDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if !strings.HasSuffix(path, ".go") || strings.HasSuffix(path, "_test.go") {
			return nil
		}
		
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, path, nil, parser.ParseComments)
		if err != nil {
			return err
		}
		
		ast.Inspect(node, func(n ast.Node) bool {
			switch x := n.(type) {
			case *ast.TypeSpec:
				if structType, ok := x.Type.(*ast.StructType); ok {
					tableName := x.Name.Name
					if isEntityStruct(structType) {
						table := &TableInfo{
							Name:     tableName,
							FileName: filepath.Base(path),
							Fields:   extractFields(structType),
							Relationships: extractRelationships(structType),
							IsJoinTable: isJoinTable(tableName),
						}
						tables[tableName] = table
					}
				}
			}
			return true
		})
		
		return nil
	})
	
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	// Print analysis
	fmt.Printf("=== DATABASE ANALYSIS ===\n\n")
	fmt.Printf("Total entities found: %d\n\n", len(tables))
	
	// Categorize tables
	coreTables := []string{}
	supportTables := []string{}
	analyticsTables := []string{}
	joinTables := []string{}
	
	for name, table := range tables {
		if table.IsJoinTable {
			joinTables = append(joinTables, name)
		} else if isCoreTable(name) {
			coreTables = append(coreTables, name)
		} else if isAnalyticsTable(name) {
			analyticsTables = append(analyticsTables, name)
		} else {
			supportTables = append(supportTables, name)
		}
	}
	
	fmt.Printf("CORE BUSINESS TABLES (%d):\n", len(coreTables))
	for _, name := range coreTables {
		fmt.Printf("  - %s\n", name)
	}
	
	fmt.Printf("\nSUPPORT/FEATURE TABLES (%d):\n", len(supportTables))
	for _, name := range supportTables {
		fmt.Printf("  - %s\n", name)
	}
	
	fmt.Printf("\nANALYTICS TABLES (%d):\n", len(analyticsTables))
	for _, name := range analyticsTables {
		fmt.Printf("  - %s\n", name)
	}
	
	fmt.Printf("\nJOIN TABLES (%d):\n", len(joinTables))
	for _, name := range joinTables {
		fmt.Printf("  - %s\n", name)
	}
	
	fmt.Printf("\n=== RECOMMENDATIONS ===\n")
	if len(analyticsTables) > 10 {
		fmt.Printf("⚠️  Too many analytics tables (%d). Consider consolidating.\n", len(analyticsTables))
	}
	if len(supportTables) > 30 {
		fmt.Printf("⚠️  Too many support tables (%d). Consider modularization.\n", len(supportTables))
	}
	if len(tables) > 80 {
		fmt.Printf("⚠️  Total tables (%d) exceeds recommended limit of 80.\n", len(tables))
	}
}

func isEntityStruct(structType *ast.StructType) bool {
	for _, field := range structType.Fields.List {
		if field.Tag != nil {
			tag := field.Tag.Value
			if strings.Contains(tag, "gorm:") || strings.Contains(tag, "json:") {
				return true
			}
		}
	}
	return false
}

func extractFields(structType *ast.StructType) []string {
	var fields []string
	for _, field := range structType.Fields.List {
		for _, name := range field.Names {
			fields = append(fields, name.Name)
		}
	}
	return fields
}

func extractRelationships(structType *ast.StructType) []string {
	var relationships []string
	for _, field := range structType.Fields.List {
		if field.Tag != nil {
			tag := field.Tag.Value
			if strings.Contains(tag, "foreignKey") || strings.Contains(tag, "many2many") {
				for _, name := range field.Names {
					relationships = append(relationships, name.Name)
				}
			}
		}
	}
	return relationships
}

func isJoinTable(name string) bool {
	joinPatterns := []string{"User", "Product", "Category"}
	count := 0
	for _, pattern := range joinPatterns {
		if strings.Contains(name, pattern) {
			count++
		}
	}
	return count >= 2
}

func isCoreTable(name string) bool {
	coreEntities := []string{
		"User", "Product", "Category", "Order", "Cart", "Payment", "Inventory",
	}
	for _, core := range coreEntities {
		if strings.Contains(name, core) {
			return true
		}
	}
	return false
}

func isAnalyticsTable(name string) bool {
	analyticsPatterns := []string{
		"Analytics", "Report", "Event", "Tracking", "Stats", "Metrics",
	}
	for _, pattern := range analyticsPatterns {
		if strings.Contains(name, pattern) {
			return true
		}
	}
	return false
}
