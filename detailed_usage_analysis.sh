#!/bin/bash

echo "=== PHÂN TÍCH CHI TIẾT MỨC ĐỘ SỬ DỤNG BẢNG ==="
echo

# L<PERSON><PERSON> danh sách tất cả bảng từ database thực tế
echo "🔍 L<PERSON>y danh sách bảng từ database..."

# Tạo danh sách bảng từ kết quả trước đó
all_tables=(
    "users" "user_profiles" "categories" "products" "brands" "carts" "cart_items"
    "orders" "order_items" "payments" "inventories" "inventory_movements"
    "reviews" "review_images" "review_votes" "notifications" "addresses"
    "coupons" "coupon_usage" "promotions" "shipments" "analytics_events"
    "email_templates" "email_subscriptions" "support_tickets" "wishlists"
    "product_images" "product_variants" "product_attributes" "shipping_methods"
    "warehouses" "file_uploads" "account_verifications" "password_resets"
)

echo "📊 Phân tích ${#all_tables[@]} bảng..."
echo

# Phân loại bảng theo mức độ sử dụng
high_usage=()      # Score >= 10
medium_usage=()    # Score 5-9
low_usage=()       # Score 1-4
no_usage=()        # Score 0

for table in "${all_tables[@]}"; do
    # Chuyển table name thành entity name
    entity_name=$(echo "$table" | sed 's/_/ /g' | sed 's/s$//' | awk '{for(i=1;i<=NF;i++) $i=toupper(substr($i,1,1)) tolower(substr($i,2))}1' | sed 's/ //g')
    
    # Tính điểm sử dụng
    score=0
    
    # Kiểm tra Entity (3 điểm)
    if grep -r --include="*.go" "type $entity_name struct" internal/domain/entities/ >/dev/null 2>&1; then
        score=$((score + 3))
    fi
    
    # Kiểm tra Repository (3 điểm)
    if find internal/infrastructure/database -name "*${table}*repository*.go" | grep -q . 2>/dev/null; then
        score=$((score + 3))
    elif find internal/infrastructure/database -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*repository*.go" | grep -q . 2>/dev/null; then
        score=$((score + 3))
    fi
    
    # Kiểm tra Usecase (2 điểm)
    if find internal/usecases -name "*${table}*usecase*.go" | grep -q . 2>/dev/null; then
        score=$((score + 2))
    elif find internal/usecases -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*usecase*.go" | grep -q . 2>/dev/null; then
        score=$((score + 2))
    fi
    
    # Kiểm tra Handler (2 điểm)
    if find internal/delivery/http -name "*${table}*handler*.go" 2>/dev/null | grep -q .; then
        score=$((score + 2))
    elif find internal/interfaces/http -name "*${table}*handler*.go" 2>/dev/null | grep -q .; then
        score=$((score + 2))
    elif find internal/delivery/http -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*handler*.go" 2>/dev/null | grep -q .; then
        score=$((score + 2))
    elif find internal/interfaces/http -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*handler*.go" 2>/dev/null | grep -q .; then
        score=$((score + 2))
    fi
    
    # Kiểm tra Code references (2 điểm)
    ref_count=$(grep -r --include="*.go" "$entity_name" . 2>/dev/null | grep -v "type $entity_name struct" | wc -l)
    if [ "$ref_count" -gt 50 ]; then
        score=$((score + 2))
    elif [ "$ref_count" -gt 10 ]; then
        score=$((score + 1))
    fi
    
    # Phân loại theo điểm
    if [ "$score" -ge 10 ]; then
        high_usage+=("$table:$score:$ref_count")
    elif [ "$score" -ge 5 ]; then
        medium_usage+=("$table:$score:$ref_count")
    elif [ "$score" -ge 1 ]; then
        low_usage+=("$table:$score:$ref_count")
    else
        no_usage+=("$table:$score:$ref_count")
    fi
done

echo "=== KẾT QUẢ PHÂN LOẠI ==="
echo

echo "🟢 BẢNG SỬ DỤNG CAO (Score >= 10) - ${#high_usage[@]} bảng:"
echo "   (Có đầy đủ Entity, Repository, Usecase, Handler)"
for item in "${high_usage[@]}"; do
    table=$(echo "$item" | cut -d':' -f1)
    score=$(echo "$item" | cut -d':' -f2)
    refs=$(echo "$item" | cut -d':' -f3)
    printf "   ✅ %-20s Score: %2d/12, Refs: %4d\n" "$table" "$score" "$refs"
done
echo

echo "🟡 BẢNG SỬ DỤNG TRUNG BÌNH (Score 5-9) - ${#medium_usage[@]} bảng:"
echo "   (Có Entity nhưng thiếu một số layer)"
for item in "${medium_usage[@]}"; do
    table=$(echo "$item" | cut -d':' -f1)
    score=$(echo "$item" | cut -d':' -f2)
    refs=$(echo "$item" | cut -d':' -f3)
    printf "   ⚠️  %-20s Score: %2d/12, Refs: %4d\n" "$table" "$score" "$refs"
done
echo

echo "🟠 BẢNG SỬ DỤNG THẤP (Score 1-4) - ${#low_usage[@]} bảng:"
echo "   (Chỉ có Entity hoặc một vài references)"
for item in "${low_usage[@]}"; do
    table=$(echo "$item" | cut -d':' -f1)
    score=$(echo "$item" | cut -d':' -f2)
    refs=$(echo "$item" | cut -d':' -f3)
    printf "   🔸 %-20s Score: %2d/12, Refs: %4d\n" "$table" "$score" "$refs"
done
echo

if [ ${#no_usage[@]} -gt 0 ]; then
    echo "🔴 BẢNG KHÔNG SỬ DỤNG (Score 0) - ${#no_usage[@]} bảng:"
    echo "   (Không có Entity, Repository, Usecase, Handler)"
    for item in "${no_usage[@]}"; do
        table=$(echo "$item" | cut -d':' -f1)
        score=$(echo "$item" | cut -d':' -f2)
        refs=$(echo "$item" | cut -d':' -f3)
        printf "   ❌ %-20s Score: %2d/12, Refs: %4d\n" "$table" "$score" "$refs"
    done
    echo
fi

echo "=== THỐNG KÊ TỔNG HỢP ==="
total_tables=${#all_tables[@]}
high_count=${#high_usage[@]}
medium_count=${#medium_usage[@]}
low_count=${#low_usage[@]}
no_count=${#no_usage[@]}

echo "📊 Phân bố sử dụng:"
echo "   🟢 Sử dụng cao:      $high_count/$total_tables   ($((high_count * 100 / total_tables))%)"
echo "   🟡 Sử dụng trung bình: $medium_count/$total_tables   ($((medium_count * 100 / total_tables))%)"
echo "   🟠 Sử dụng thấp:     $low_count/$total_tables   ($((low_count * 100 / total_tables))%)"
echo "   🔴 Không sử dụng:    $no_count/$total_tables   ($((no_count * 100 / total_tables))%)"
echo

echo "=== KHUYẾN NGHỊ HÀNH ĐỘNG ==="
echo

if [ $high_count -gt 0 ]; then
    echo "✅ GIỮ LẠI ($high_count bảng):"
    echo "   - Các bảng có điểm cao (>= 10) là core business"
    echo "   - Được sử dụng đầy đủ trong tất cả layers"
    echo "   - Cần thiết cho hoạt động hệ thống"
fi

if [ $medium_count -gt 0 ]; then
    echo "🔧 CẢI THIỆN ($medium_count bảng):"
    echo "   - Có Entity nhưng thiếu Repository/Usecase/Handler"
    echo "   - Cần implement đầy đủ các layer hoặc xem xét loại bỏ"
    echo "   - Có thể là feature chưa hoàn thiện"
fi

if [ $low_count -gt 0 ]; then
    echo "⚠️  XEM XÉT ($low_count bảng):"
    echo "   - Chỉ có Entity hoặc ít references"
    echo "   - Có thể là bảng dự phòng hoặc chưa sử dụng"
    echo "   - Cân nhắc loại bỏ nếu không có kế hoạch phát triển"
fi

if [ $no_count -gt 0 ]; then
    echo "❌ LOẠI BỎ ($no_count bảng):"
    echo "   - Không có implementation nào"
    echo "   - An toàn để xóa khỏi database"
    echo "   - Giảm complexity và tăng performance"
fi

echo
echo "🎯 MỤC TIÊU TỐI ƯU:"
optimal_count=$((high_count + (medium_count / 2)))
echo "   - Số bảng tối ưu: ~$optimal_count bảng (thay vì $total_tables)"
echo "   - Giảm: $((total_tables - optimal_count)) bảng ($((100 - optimal_count * 100 / total_tables))%)"
echo "   - Tập trung vào $high_count bảng core + cải thiện $medium_count bảng medium"
