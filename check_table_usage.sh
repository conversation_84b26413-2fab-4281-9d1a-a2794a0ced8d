#!/bin/bash

echo "=== PHÂN TÍCH SỬ DỤNG BẢNG THỰC TẾ TRONG HỆ THỐNG ==="
echo

# Danh sách các bảng ch<PERSON>h từ database
tables=(
    "users" "user_profiles" "categories" "products" "brands"
    "carts" "cart_items" "orders" "order_items" "payments"
    "inventories" "inventory_movements" "reviews" "notifications" 
    "addresses" "coupons" "promotions" "shipments" "analytics_events"
    "email_templates" "support_tickets" "wishlists" "product_images"
    "product_variants" "shipping_methods" "warehouses"
)

echo "🔍 Kiểm tra ${#tables[@]} bảng chính..."
echo

used_tables=()
unused_tables=()

for table in "${tables[@]}"; do
    # Chuyển table name thành entity name (User, Product, etc.)
    entity_name=$(echo "$table" | sed 's/_/ /g' | sed 's/s$//' | awk '{for(i=1;i<=NF;i++) $i=toupper(substr($i,1,1)) tolower(substr($i,2))}1' | sed 's/ //g')
    
    echo "📋 Kiểm tra bảng: $table -> Entity: $entity_name"
    
    # Kiểm tra Entity
    entity_exists=false
    if grep -r --include="*.go" "type $entity_name struct" internal/domain/entities/ >/dev/null 2>&1; then
        entity_exists=true
        echo "   ✅ Entity: Có"
    else
        echo "   ❌ Entity: Không"
    fi
    
    # Kiểm tra Repository
    repo_exists=false
    if find internal/infrastructure/database -name "*${table}*repository*.go" | grep -q .; then
        repo_exists=true
        echo "   ✅ Repository: Có"
    elif find internal/infrastructure/database -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*repository*.go" | grep -q .; then
        repo_exists=true
        echo "   ✅ Repository: Có"
    else
        echo "   ❌ Repository: Không"
    fi
    
    # Kiểm tra Usecase
    usecase_exists=false
    if find internal/usecases -name "*${table}*usecase*.go" | grep -q .; then
        usecase_exists=true
        echo "   ✅ Usecase: Có"
    elif find internal/usecases -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*usecase*.go" | grep -q .; then
        usecase_exists=true
        echo "   ✅ Usecase: Có"
    else
        echo "   ❌ Usecase: Không"
    fi
    
    # Kiểm tra Handler
    handler_exists=false
    if find internal/delivery/http -name "*${table}*handler*.go" 2>/dev/null | grep -q .; then
        handler_exists=true
        echo "   ✅ Handler: Có"
    elif find internal/interfaces/http -name "*${table}*handler*.go" 2>/dev/null | grep -q .; then
        handler_exists=true
        echo "   ✅ Handler: Có"
    elif find internal/delivery/http -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*handler*.go" 2>/dev/null | grep -q .; then
        handler_exists=true
        echo "   ✅ Handler: Có"
    elif find internal/interfaces/http -name "*$(echo $entity_name | tr '[:upper:]' '[:lower:]')*handler*.go" 2>/dev/null | grep -q .; then
        handler_exists=true
        echo "   ✅ Handler: Có"
    else
        echo "   ❌ Handler: Không"
    fi
    
    # Đếm references trong code
    ref_count=$(grep -r --include="*.go" "$entity_name" . 2>/dev/null | grep -v "type $entity_name struct" | wc -l)
    echo "   📝 Code references: $ref_count"
    
    # Tính điểm sử dụng
    score=0
    if [ "$entity_exists" = true ]; then score=$((score + 3)); fi
    if [ "$repo_exists" = true ]; then score=$((score + 3)); fi
    if [ "$usecase_exists" = true ]; then score=$((score + 2)); fi
    if [ "$handler_exists" = true ]; then score=$((score + 2)); fi
    if [ "$ref_count" -gt 10 ]; then score=$((score + 2)); elif [ "$ref_count" -gt 5 ]; then score=$((score + 1)); fi
    
    echo "   🎯 Usage Score: $score/12"
    
    if [ "$score" -gt 0 ]; then
        used_tables+=("$table:$score")
    else
        unused_tables+=("$table")
    fi
    
    echo
done

echo "=== KẾT QUẢ PHÂN TÍCH ==="
echo

echo "✅ BẢNG ĐƯỢC SỬ DỤNG TRONG HỆ THỐNG (${#used_tables[@]}):"
for item in "${used_tables[@]}"; do
    table_name=$(echo "$item" | cut -d':' -f1)
    score=$(echo "$item" | cut -d':' -f2)
    echo "   🔹 $table_name (Score: $score/12)"
done
echo

if [ ${#unused_tables[@]} -gt 0 ]; then
    echo "❌ BẢNG KHÔNG SỬ DỤNG TRONG HỆ THỐNG (${#unused_tables[@]}):"
    for table in "${unused_tables[@]}"; do
        echo "   - $table"
    done
    echo
fi

echo "=== PHÂN TÍCH CHI TIẾT THEO LAYER ==="
echo

echo "📁 REPOSITORIES ĐƯỢC IMPLEMENT:"
find internal/infrastructure/database -name "*_repository.go" | sed 's/.*\//   ✅ /' | sed 's/_repository.go//'
echo

echo "🔧 USECASES ĐƯỢC IMPLEMENT:"
find internal/usecases -name "*_usecase.go" | grep -v ".bak\|.tmp" | sed 's/.*\//   ✅ /' | sed 's/_usecase.go//'
echo

echo "🌐 HANDLERS ĐƯỢC IMPLEMENT:"
find internal/delivery/http -name "*_handler.go" 2>/dev/null | sed 's/.*\//   ✅ /' | sed 's/_handler.go//'
find internal/interfaces/http -name "*_handler.go" 2>/dev/null | sed 's/.*\//   ✅ /' | sed 's/_handler.go//'
echo

echo "=== KHUYẾN NGHỊ ==="
used_count=${#used_tables[@]}
unused_count=${#unused_tables[@]}
total_count=$((used_count + unused_count))

echo "📊 Thống kê:"
echo "   - Tổng bảng kiểm tra: $total_count"
echo "   - Bảng được sử dụng: $used_count ($((used_count * 100 / total_count))%)"
echo "   - Bảng không sử dụng: $unused_count ($((unused_count * 100 / total_count))%)"
echo

if [ $unused_count -gt 0 ]; then
    echo "🔧 Có thể xem xét loại bỏ $unused_count bảng không sử dụng"
fi

if [ $used_count -lt 15 ]; then
    echo "✅ Số lượng bảng được sử dụng ($used_count) trong mức hợp lý"
else
    echo "⚠️  Số lượng bảng được sử dụng ($used_count) khá nhiều, xem xét tối ưu hóa"
fi
